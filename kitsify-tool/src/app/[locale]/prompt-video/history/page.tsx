'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { type PromptVideoHistory, promptVideoService } from '@/services/prompt-video';

export default function PromptVideoHistoryPage() {
  const t = useTranslations('PromptVideo');
  const [histories, setHistories] = useState<PromptVideoHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [expandedItems, setExpandedItems] = useState(new Set());
  const [searchText, setSearchText] = useState('');

  const fetchHistories = async () => {
    try {
      setLoading(true);
      const response = await promptVideoService.getPromptVideoHistories({
        page,
        pageSize: 10,
        search_text: searchText || undefined,
      });

      setHistories(response.data);
      setTotalPages(response.pagination.totalPages);
      setError('');
    } catch (err) {
      console.error('Error fetching histories:', err);
      setError('Failed to load prompt video histories');
      toast.error('Failed to load prompt video histories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistories();
  }, [page]);

  const toggleExpanded = (id) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="rounded-lg bg-white p-6 shadow-lg">
          <div className="flex h-64 items-center justify-center">
            <div className="size-12 animate-spin rounded-full border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-600">Loading histories...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          {t('history_title')}
        </h1>
        <p className="mt-2 text-gray-600">
          View and manage your previously generated video prompts.
        </p>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-sm">

        {error && (
          <div className="mb-6 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
            {error}
          </div>
        )}

        {histories.length === 0
          ? (
              <div className="py-12 text-center">
                <div className="mb-4 text-lg text-gray-500">
                  No prompt histories found
                </div>
                <p className="text-gray-400">
                  Start generating video prompts to see your history here.
                </p>
              </div>
            )
          : (
              <div className="space-y-4">
                {histories.map(history => (
                  <div key={history.id} className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
                    <div className="mb-3 flex items-start justify-between">
                      <div className="flex-1">
                        <div className="mb-1 text-sm text-gray-500">
                          {formatDate(history.created_at)}
                          {' '}
                          • Model:
                          {history.model}
                        </div>
                        <div className="mb-2 font-medium text-gray-800">
                          Prompt Data Summary
                        </div>
                        <div className="rounded border bg-gray-50 p-3 text-sm text-gray-700">
                          {history.prompt_data.location && `Location: ${history.prompt_data.location}`}
                          {history.prompt_data.location && history.prompt_data.time && ', '}
                          {history.prompt_data.time && `Time: ${history.prompt_data.time}`}
                          {(history.prompt_data.location || history.prompt_data.time) && history.prompt_data.weather && ', '}
                          {history.prompt_data.weather && `Weather: ${history.prompt_data.weather}`}
                          {(history.prompt_data.location || history.prompt_data.time || history.prompt_data.weather)
                          && (history.prompt_data.visualStyle?.length > 0 || history.prompt_data.mood?.length > 0) && '. '}
                          {history.prompt_data.visualStyle?.length > 0 && `Style: ${history.prompt_data.visualStyle.join(', ')}`}
                          {history.prompt_data.visualStyle?.length > 0 && history.prompt_data.mood?.length > 0 && ', '}
                          {history.prompt_data.mood?.length > 0 && `Mood: ${history.prompt_data.mood.join(', ')}`}
                        </div>
                      </div>
                      <button
                        onClick={() => toggleExpanded(history.id)}
                        className="ml-4 p-2 text-gray-500 transition-colors hover:text-gray-700"
                      >
                        <svg
                          className={`size-5 transition-transform${expandedItems.has(history.id) ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                    </div>

                    {expandedItems.has(history.id) && (
                      <div className="mt-4 border-t border-gray-200 pt-4">
                        <div className="mb-2 flex items-center justify-between">
                          <div className="font-medium text-gray-800">
                            Generated Result
                          </div>
                          <button
                            onClick={() => copyToClipboard(history.generated_result)}
                            className="text-sm font-medium text-blue-600 transition-colors hover:text-blue-800"
                          >
                            Copy Result
                          </button>
                        </div>
                        <div className="rounded border border-blue-200 bg-blue-50 p-3 text-sm text-gray-700">
                          {history.generated_result}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <div className="flex space-x-2">
              <button
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                Previous
              </button>
              <span className="rounded-md border border-gray-300 bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700">
                Page
                {' '}
                {page}
                {' '}
                of
                {totalPages}
              </span>
              <button
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
