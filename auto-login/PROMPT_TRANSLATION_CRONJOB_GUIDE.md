# Prompt Translation Cronjob System

## Tổng quan

Hệ thống cronjob translation tự động dịch các prompts từ bảng `prompts` sang bảng `prompt_translations` một cách nhanh chóng và đảm bảo giữ nguyên cấu trúc markdown.

## Tính năng chính

### 1. Tự động hóa hoàn toàn
- **Cronjob tự động**: Chạy mỗi ngày lúc 2:00 AM (Asia/Ho_Chi_Minh timezone)
- **Batch processing**: Xử lý theo từng batch để tối ưu hiệu suất
- **Parallel processing**: Xử lý đồng thời nhiều batch để tăng tốc độ

### 2. Tối ưu hiệu suất
- **Database indexing**: Tạo index cho các query tìm prompts chưa dịch
- **Bulk operations**: Sử dụng bulk insert để lưu translations nhanh hơn
- **Connection pooling**: T<PERSON>i ưu database connections
- **Rate limiting**: <PERSON><PERSON><PERSON> so<PERSON><PERSON> tần suất gọi API để tránh hit rate limit

### 3. Xử lý lỗi và retry
- **Retry mechanism**: Tự động retry với exponential backoff
- **Error handling**: Xử lý lỗi chi tiết và logging
- **Transaction safety**: Sử dụng database transaction để đảm bảo data consistency
- **Race condition protection**: Kiểm tra duplicate trước khi insert

### 4. Monitoring và logging
- **Chi tiết logging**: Log progress, errors, và statistics
- **Health check**: Endpoint để check status của cronjob
- **Statistics tracking**: Theo dõi số lượng translations thành công/thất bại

## Cấu trúc file

```
src/modules/prompts/
├── services/
│   └── prompt-translation-cron.service.ts    # Service chính xử lý cronjob
├── prompt-translation-cron.controller.ts     # Controller cho manual trigger và status
└── prompts.module.ts                          # Module configuration

src/migrations/
└── 1704100000000-AddPromptTranslationIndexes.ts  # Database indexes

PROMPT_TRANSLATION_CRONJOB_GUIDE.md           # Documentation này
```

## Cấu hình Environment Variables

```bash
# Translation Cron Configuration
ENABLE_TRANSLATION_CRON=true              # Bật/tắt cronjob
TRANSLATION_BATCH_SIZE=10                  # Số prompts xử lý trong 1 batch
TRANSLATION_MAX_RETRIES=3                  # Số lần retry khi thất bại
TRANSLATION_RETRY_DELAY=5000               # Delay giữa các retry (ms)
TRANSLATION_LANGUAGES=en,th,fr             # Danh sách ngôn ngữ cần dịch
TRANSLATION_PARALLEL_BATCHES=3             # Số batch xử lý đồng thời
```

## API Endpoints

### 1. Manual Trigger
```http
POST /admin/prompt-translation/trigger
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Translation job completed successfully",
  "stats": {
    "totalPrompts": 150,
    "successfulTranslations": 145,
    "failedTranslations": 3,
    "skippedTranslations": 2,
    "processingTime": 45000
  }
}
```

### 2. Status Check
```http
GET /admin/prompt-translation/status
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "isRunning": false,
    "supportedLanguages": ["en", "th", "fr"]
  }
}
```

### 3. Health Check
```http
GET /admin/prompt-translation/health
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Prompt Translation Cron Service is healthy",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Cách hoạt động

### 1. Phát hiện prompts cần dịch
- Query tìm prompts chưa có bản dịch cho từng ngôn ngữ
- Sử dụng LEFT JOIN để tìm prompts thiếu translations
- Lọc prompts có title không null và không rỗng

### 2. Xử lý theo batch
- Chia prompts thành các batch nhỏ (configurable size)
- Xử lý nhiều batch đồng thời (parallel processing)
- Delay giữa các batch group để tránh overload API

### 3. Translation process
- Gọi OpenRouter API với instruction giữ nguyên markdown
- Retry với exponential backoff khi thất bại
- Kiểm tra duplicate trước khi lưu (race condition protection)

### 4. Lưu trữ kết quả
- Bulk insert translations để tối ưu performance
- Sử dụng database transaction để đảm bảo consistency
- Log chi tiết kết quả xử lý

## Database Schema

### Indexes được tạo:
```sql
-- Index cho việc tìm prompts chưa dịch
CREATE INDEX "IDX_prompts_title_not_null" 
ON "prompts" ("id") 
WHERE "title" IS NOT NULL AND "title" != '';

-- Index cho lookup translations
CREATE INDEX "IDX_prompt_translations_prompt_lang" 
ON "prompt_translations" ("prompt_id", "lang");

-- Index cho filter theo ngôn ngữ
CREATE INDEX "IDX_prompt_translations_lang" 
ON "prompt_translations" ("lang");

-- Index cho ordering prompts
CREATE INDEX "IDX_prompts_id_asc" 
ON "prompts" ("id" ASC);
```

## Cách sử dụng

### 1. Chạy migration
```bash
npm run migration:run
```

### 2. Khởi động ứng dụng
```bash
npm run start:dev  # Development
npm run start:prod # Production
```

### 3. Kiểm tra status
```bash
curl -H "Authorization: Bearer <token>" \
     http://localhost:3000/admin/prompt-translation/status
```

### 4. Trigger manual (nếu cần)
```bash
curl -X POST \
     -H "Authorization: Bearer <token>" \
     http://localhost:3000/admin/prompt-translation/trigger
```

## Monitoring và troubleshooting

### 1. Logs để theo dõi
- Translation job start/completion
- Batch processing progress
- Individual translation success/failure
- API rate limiting issues
- Database transaction errors

### 2. Common issues
- **API rate limit**: Tăng delay giữa các batch
- **Database timeout**: Giảm batch size
- **Memory issues**: Giảm parallel batches
- **Translation quality**: Kiểm tra OpenRouter API response

### 3. Performance tuning
- Điều chỉnh `TRANSLATION_BATCH_SIZE` dựa trên database performance
- Điều chỉnh `TRANSLATION_PARALLEL_BATCHES` dựa trên API rate limit
- Điều chỉnh `TRANSLATION_RETRY_DELAY` dựa trên network latency

## Lưu ý quan trọng

1. **Markdown preservation**: System được thiết kế để giữ nguyên cấu trúc markdown
2. **Source language**: Vietnamese (vi) được coi là ngôn ngữ gốc, không cần dịch
3. **Duplicate prevention**: System tự động kiểm tra và tránh duplicate translations
4. **Resource management**: Sử dụng connection pooling và transaction để tối ưu database
5. **Error recovery**: Hệ thống có khả năng phục hồi từ lỗi và tiếp tục xử lý

## Tích hợp với hệ thống hiện tại

Cronjob translation system được tích hợp hoàn toàn với:
- Existing translation service
- OpenRouter API configuration
- Database schema hiện tại
- Authentication system
- Logging infrastructure
