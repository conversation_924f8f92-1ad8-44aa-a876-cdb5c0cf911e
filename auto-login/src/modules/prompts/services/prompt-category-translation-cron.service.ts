import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { PromptCategory } from '../entities/prompt-category.entity';
import { PromptCategoryTranslation } from '../entities/prompt-category-translation.entity';
import { TranslationService } from './translation.service';
import { ConfigService } from '@nestjs/config';

interface CategoryTranslationBatch {
  categories: PromptCategory[];
  language: string;
}

export interface CategoryTranslationStats {
  totalCategories: number;
  successfulTranslations: number;
  failedTranslations: number;
  skippedTranslations: number;
  processingTime: number;
}

@Injectable()
export class PromptCategoryTranslationCronService {
  private readonly logger = new Logger(
    PromptCategoryTranslationCronService.name,
  );
  private isRunning = false;

  // Configuration from environment variables
  private readonly batchSize: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;
  private readonly supportedLanguages: string[];
  private readonly enableCron: boolean;
  private readonly parallelBatches: number;

  // Circuit breaker for translation failures
  private translationFailureCount = 0;
  private lastFailureTime = 0;
  private readonly circuitBreakerThreshold = 10; // Open circuit after 10 consecutive failures
  private readonly circuitBreakerTimeout = 300000; // 5 minutes

  constructor(
    @InjectRepository(PromptCategory)
    private readonly promptCategoryRepository: Repository<PromptCategory>,
    @InjectRepository(PromptCategoryTranslation)
    private readonly promptCategoryTranslationRepository: Repository<PromptCategoryTranslation>,
    private readonly translationService: TranslationService,
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
  ) {
    // Load configuration
    this.batchSize = parseInt(
      this.configService.get('TRANSLATION_BATCH_SIZE', '10'),
    );
    this.maxRetries = parseInt(
      this.configService.get('TRANSLATION_MAX_RETRIES', '3'),
    );
    this.retryDelay = parseInt(
      this.configService.get('TRANSLATION_RETRY_DELAY', '5000'),
    );
    this.supportedLanguages = (
      this.configService.get('TRANSLATION_LANGUAGES', 'en') || 'en'
    ).split(',');
    this.enableCron =
      this.configService.get('ENABLE_TRANSLATION_CRON', 'true') === 'true';
    this.parallelBatches = parseInt(
      this.configService.get('TRANSLATION_PARALLEL_BATCHES', '3'),
    );

    this.logger.log(
      `Category Translation Cron Service initialized with config:`,
      {
        batchSize: this.batchSize,
        maxRetries: this.maxRetries,
        retryDelay: this.retryDelay,
        supportedLanguages: this.supportedLanguages,
        enableCron: this.enableCron,
        parallelBatches: this.parallelBatches,
      },
    );

    this.logger.log(
      '🔄 Category Translation Cron Job scheduled to run at 11:25 AM daily (Asia/Ho_Chi_Minh timezone)',
    );
  }

  // Production schedule (uncomment for production use):
  // @Cron('0 15 2 * * *', {
  //   name: 'category-translation',
  //   timeZone: 'Asia/Ho_Chi_Minh',
  // })

  // Production schedule - run at 13:15:00 PM every day (uncomment for production)
  @Cron('0 48 13 * * *', {
    name: 'category-translation',
    timeZone: 'Asia/Ho_Chi_Minh',
  })
  async handleCategoryTranslationCron() {
    this.logger.log('🔄 Category translation cron triggered!');

    if (!this.enableCron) {
      this.logger.warn(
        '⚠️ Category translation cron is disabled via ENABLE_TRANSLATION_CRON config',
      );
      return;
    }

    if (this.isRunning) {
      this.logger.warn(
        '⚠️ Category translation cron is already running, skipping this execution',
      );
      return;
    }

    this.logger.log('✅ Starting scheduled category translation job...');
    try {
      await this.runCategoryTranslationJob();
      this.logger.log(
        '🎉 Scheduled category translation job completed successfully',
      );
    } catch (error) {
      this.logger.error('❌ Scheduled category translation job failed:', error);
    }
  }

  /**
   * Manual trigger for category translation job (for testing and manual execution)
   */
  async triggerManualCategoryTranslation(): Promise<CategoryTranslationStats> {
    if (this.isRunning) {
      throw new Error('Category translation job is already running');
    }

    this.logger.log('Starting manual category translation job...');
    return await this.runCategoryTranslationJob();
  }

  /**
   * Main category translation job execution
   */
  private async runCategoryTranslationJob(): Promise<CategoryTranslationStats> {
    const startTime = Date.now();
    this.isRunning = true;

    const stats: CategoryTranslationStats = {
      totalCategories: 0,
      successfulTranslations: 0,
      failedTranslations: 0,
      skippedTranslations: 0,
      processingTime: 0,
    };

    try {
      this.logger.log('Analyzing categories requiring translation...');

      // Get translation batches for all languages
      const translationBatches = await this.getCategoryTranslationBatches();

      if (translationBatches.length === 0) {
        this.logger.log('No categories require translation at this time');
        return stats;
      }

      stats.totalCategories = translationBatches.reduce(
        (total, batch) => total + batch.categories.length,
        0,
      );

      this.logger.log(
        `Found ${stats.totalCategories} categories requiring translation across ${translationBatches.length} language batches`,
      );

      // Process batches in parallel groups
      await this.processCategoryBatchesInParallel(translationBatches, stats);

      stats.processingTime = Date.now() - startTime;

      this.logger.log('Category translation job completed successfully', {
        stats,
        duration: `${(stats.processingTime / 1000).toFixed(2)}s`,
      });

      return stats;
    } catch (error) {
      stats.processingTime = Date.now() - startTime;
      this.logger.error('Category translation job failed:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Get batches of categories that need translation for each language
   */
  private async getCategoryTranslationBatches(): Promise<
    CategoryTranslationBatch[]
  > {
    const batches: CategoryTranslationBatch[] = [];

    this.logger.log(
      `🔍 Checking translation needs for languages: ${this.supportedLanguages.join(', ')}`,
    );

    for (const language of this.supportedLanguages) {
      // Skip Vietnamese as it's the source language
      if (language === 'vi') {
        this.logger.log(`⏭️ Skipping Vietnamese (source language)`);
        continue;
      }

      this.logger.log(
        `🔍 Checking categories needing translation to ${language}...`,
      );

      // Find categories that don't have translations for this language
      const unTranslatedCategories = await this.promptCategoryRepository
        .createQueryBuilder('category')
        .leftJoin(
          'prompt_categories_translations',
          'translation',
          'translation.category_id = category.id AND translation.lang = :lang',
          { lang: language },
        )
        .where('translation.id IS NULL')
        .andWhere('category.name IS NOT NULL')
        .andWhere('category.name != :empty', { empty: '' })
        .orderBy('category.id', 'ASC')
        .limit(this.batchSize * 5) // Get more categories to create multiple batches
        .getMany();

      this.logger.log(
        `📊 Found ${unTranslatedCategories.length} categories needing translation to ${language}`,
      );

      if (unTranslatedCategories.length > 0) {
        // Split into smaller batches
        for (
          let i = 0;
          i < unTranslatedCategories.length;
          i += this.batchSize
        ) {
          const batchCategories = unTranslatedCategories.slice(
            i,
            i + this.batchSize,
          );
          batches.push({
            categories: batchCategories,
            language,
          });
        }
      }
    }

    this.logger.log(`📦 Total batches created: ${batches.length}`);
    return batches;
  }

  /**
   * Process category translation batches in parallel groups
   */
  private async processCategoryBatchesInParallel(
    batches: CategoryTranslationBatch[],
    stats: CategoryTranslationStats,
  ): Promise<void> {
    // Process batches in parallel groups to avoid overwhelming the API
    for (let i = 0; i < batches.length; i += this.parallelBatches) {
      const batchGroup = batches.slice(i, i + this.parallelBatches);

      this.logger.log(
        `Processing category batch group ${Math.floor(i / this.parallelBatches) + 1}/${Math.ceil(batches.length / this.parallelBatches)} (${batchGroup.length} batches)`,
      );

      // Process batches in parallel
      const batchPromises = batchGroup.map((batch) =>
        this.processCategoryBatch(batch, stats),
      );

      const results = await Promise.allSettled(batchPromises);

      // Log results for this batch group
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          this.logger.error(
            `Category batch ${i + index + 1} failed:`,
            result.reason,
          );
        }
      });

      // Add delay between batch groups to respect rate limits
      if (i + this.parallelBatches < batches.length) {
        await this.delay(2000); // 2 second delay between batch groups
      }
    }
  }

  /**
   * Process a single category translation batch
   */
  private async processCategoryBatch(
    batch: CategoryTranslationBatch,
    stats: CategoryTranslationStats,
  ): Promise<void> {
    this.logger.log(
      `Processing category batch: ${batch.categories.length} categories for language '${batch.language}'`,
    );

    const translations: PromptCategoryTranslation[] = [];

    for (const category of batch.categories) {
      try {
        const translation = await this.translateCategoryWithRetry(
          category,
          batch.language,
        );
        if (translation) {
          translations.push(translation);
          stats.successfulTranslations++;
        } else {
          stats.skippedTranslations++;
        }
      } catch (error) {
        this.logger.error(
          `Failed to translate category ${category.id} to ${batch.language}:`,
          error.message,
        );
        stats.failedTranslations++;
      }

      // Delay between individual translations
      await this.delay(1000);
    }

    // Bulk save translations
    if (translations.length > 0) {
      await this.bulkSaveCategoryTranslations(translations);
      this.logger.log(
        `Successfully saved ${translations.length} category translations for language '${batch.language}'`,
      );
    }
  }

  /**
   * Translate a single category with retry logic
   * Translates each field individually (title and short_description)
   */
  private async translateCategoryWithRetry(
    category: PromptCategory,
    targetLanguage: string,
    attempt: number = 1,
  ): Promise<PromptCategoryTranslation | null> {
    try {
      // Check if translation already exists (race condition protection)
      const existingTranslation =
        await this.promptCategoryTranslationRepository.findOne({
          where: { category_id: category.id, lang: targetLanguage },
        });

      if (existingTranslation) {
        this.logger.debug(
          `Translation already exists for category ${category.id} in ${targetLanguage}`,
        );
        return null;
      }

      this.logger.debug(
        `Starting individual field translation for category ${category.id} to ${targetLanguage}`,
      );

      // Translate title field (name -> title) with individual retry
      const translatedTitle = await this.translateFieldWithRetry(
        category.name,
        'title',
        targetLanguage,
        category.id,
        attempt,
      );

      // Small delay between field translations
      await this.delay(200);

      // Translate description field (description -> short_description) with individual retry
      const translatedShortDescription = category.description
        ? await this.translateFieldWithRetry(
            category.description,
            'short_description',
            targetLanguage,
            category.id,
            attempt,
          )
        : null;

      this.logger.debug(
        `Completed individual field translation for category ${category.id} to ${targetLanguage}`,
      );

      // Create translation entity
      const translation = new PromptCategoryTranslation();
      translation.category_id = category.id;
      translation.lang = targetLanguage;
      translation.title = translatedTitle;
      translation.short_description = translatedShortDescription;

      return translation;
    } catch (error) {
      if (attempt < this.maxRetries) {
        this.logger.warn(
          `Category translation attempt ${attempt} failed for category ${category.id} (${targetLanguage}), retrying...`,
        );
        await this.delay(this.retryDelay * attempt); // Exponential backoff
        return this.translateCategoryWithRetry(
          category,
          targetLanguage,
          attempt + 1,
        );
      }
      throw error;
    }
  }

  /**
   * Bulk save category translations using database transaction
   */
  private async bulkSaveCategoryTranslations(
    translations: PromptCategoryTranslation[],
  ): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await queryRunner.manager.save(PromptCategoryTranslation, translations);
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get category translation job status
   */
  getStatus(): { isRunning: boolean; supportedLanguages: string[] } {
    return {
      isRunning: this.isRunning,
      supportedLanguages: this.supportedLanguages,
    };
  }

  /**
   * Translate individual field with retry logic and fallback strategies
   */
  private async translateFieldWithRetry(
    fieldValue: string,
    fieldName: string,
    targetLanguage: string,
    categoryId: number,
    parentAttempt: number = 1,
  ): Promise<string> {
    const maxFieldRetries = 3; // Separate retry count for individual fields

    // Check circuit breaker before attempting translation
    if (this.isCircuitBreakerOpen()) {
      this.logger.warn(
        `Circuit breaker is open, skipping translation for ${fieldName} (category ${categoryId})`,
      );
      throw new Error('Translation service circuit breaker is open');
    }

    for (let fieldAttempt = 1; fieldAttempt <= maxFieldRetries; fieldAttempt++) {
      try {
        this.logger.debug(
          `Translating ${fieldName} for category ${categoryId} (parent attempt: ${parentAttempt}, field attempt: ${fieldAttempt})`,
        );

        const result = await this.translationService.translateSingleField(
          fieldValue,
          fieldName,
          targetLanguage,
        );

        this.logger.debug(
          `Successfully translated ${fieldName} for category ${categoryId}`,
        );

        // Reset circuit breaker on success
        this.resetCircuitBreaker();

        return result;
      } catch (error) {
        const isLastAttempt = fieldAttempt === maxFieldRetries;
        const errorMessage = error.response?.data?.error?.message || error.message;

        this.logger.warn(
          `Field ${fieldName} translation attempt ${fieldAttempt}/${maxFieldRetries} failed for category ${categoryId}: ${errorMessage}`,
        );

        // Record failure for circuit breaker
        this.recordTranslationFailure();

        if (isLastAttempt) {
          // Apply fallback strategies before giving up
          const fallbackResult = await this.applyFallbackStrategies(
            fieldValue,
            fieldName,
            targetLanguage,
            categoryId,
            errorMessage,
          );

          if (fallbackResult) {
            return fallbackResult;
          }

          // If all fallbacks fail, throw the original error
          throw new Error(
            `Failed to translate ${fieldName} for category ${categoryId} after ${maxFieldRetries} attempts and fallbacks: ${errorMessage}`,
          );
        }

        // Calculate delay with exponential backoff and jitter
        const baseDelay = 1000; // 1 second base delay
        const exponentialDelay = baseDelay * Math.pow(2, fieldAttempt - 1);
        const jitter = Math.random() * 1000; // Add up to 1 second jitter
        const totalDelay = Math.min(exponentialDelay + jitter, 10000); // Cap at 10 seconds

        this.logger.debug(
          `Waiting ${Math.round(totalDelay)}ms before retrying ${fieldName} translation...`,
        );

        await this.delay(totalDelay);
      }
    }

    // This should never be reached, but just in case
    throw new Error(`Unexpected error in translateFieldWithRetry for ${fieldName}`);
  }

  /**
   * Apply fallback strategies when translation fails
   */
  private async applyFallbackStrategies(
    fieldValue: string,
    fieldName: string,
    targetLanguage: string,
    categoryId: number,
    originalError: string,
  ): Promise<string | null> {
    this.logger.warn(
      `Applying fallback strategies for ${fieldName} translation (category ${categoryId})`,
    );

    // Strategy 1: Try with simplified prompt
    try {
      this.logger.debug(`Fallback 1: Trying simplified translation for ${fieldName}`);

      // Use a simpler, more direct translation approach
      const simplifiedResult = await this.translationService.translateSingleField(
        fieldValue.substring(0, 200), // Limit length to reduce complexity
        fieldName,
        targetLanguage,
      );

      this.logger.log(
        `Fallback 1 successful for ${fieldName} (category ${categoryId})`,
      );

      return simplifiedResult;
    } catch (fallbackError1) {
      this.logger.debug(
        `Fallback 1 failed for ${fieldName}: ${fallbackError1.message}`,
      );
    }

    // Strategy 2: Return original text with language indicator (better than complete failure)
    if (fieldName === 'title') {
      const fallbackTitle = `${fieldValue} (${targetLanguage.toUpperCase()})`;
      this.logger.warn(
        `Fallback 2: Using original title with language indicator for category ${categoryId}`,
      );
      return fallbackTitle;
    }

    // Strategy 3: For description, try to return a shortened version
    if (fieldName === 'short_description' && fieldValue.length > 50) {
      const shortenedValue = fieldValue.substring(0, 50) + '...';
      this.logger.warn(
        `Fallback 3: Using shortened description for category ${categoryId}`,
      );
      return shortenedValue;
    }

    // Strategy 4: Return original value as last resort
    this.logger.error(
      `All fallback strategies failed for ${fieldName} (category ${categoryId}). Using original value.`,
    );

    return fieldValue; // Return original value rather than null
  }

  /**
   * Circuit breaker methods for handling translation service failures
   */
  private isCircuitBreakerOpen(): boolean {
    if (this.translationFailureCount < this.circuitBreakerThreshold) {
      return false;
    }

    const timeSinceLastFailure = Date.now() - this.lastFailureTime;
    if (timeSinceLastFailure > this.circuitBreakerTimeout) {
      // Reset circuit breaker after timeout
      this.resetCircuitBreaker();
      return false;
    }

    return true;
  }

  private recordTranslationFailure(): void {
    this.translationFailureCount++;
    this.lastFailureTime = Date.now();

    if (this.translationFailureCount >= this.circuitBreakerThreshold) {
      this.logger.error(
        `🚨 Translation service circuit breaker opened after ${this.translationFailureCount} consecutive failures`,
      );
    }
  }

  private resetCircuitBreaker(): void {
    if (this.translationFailureCount > 0) {
      this.logger.log(
        `✅ Translation service circuit breaker reset after successful translation`,
      );
    }
    this.translationFailureCount = 0;
    this.lastFailureTime = 0;
  }

  /**
   * Get circuit breaker status for monitoring
   */
  getCircuitBreakerStatus(): {
    isOpen: boolean;
    failureCount: number;
    lastFailureTime: number;
    threshold: number;
    timeout: number;
  } {
    return {
      isOpen: this.isCircuitBreakerOpen(),
      failureCount: this.translationFailureCount,
      lastFailureTime: this.lastFailureTime,
      threshold: this.circuitBreakerThreshold,
      timeout: this.circuitBreakerTimeout,
    };
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
