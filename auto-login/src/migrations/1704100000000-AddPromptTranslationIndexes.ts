import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromptTranslationIndexes1704100000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add index for efficient querying of prompts without translations
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_prompts_title_not_null" 
      ON "prompts" ("id") 
      WHERE "title" IS NOT NULL AND "title" != ''
    `);

    // Add index for prompt_translations lookup by prompt_id and lang
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_prompt_translations_prompt_lang" 
      ON "prompt_translations" ("prompt_id", "lang")
    `);

    // Add index for prompt_translations lookup by lang only
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_prompt_translations_lang" 
      ON "prompt_translations" ("lang")
    `);

    // Add index for prompts ordering by id
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS "IDX_prompts_id_asc" 
      ON "prompts" ("id" ASC)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes in reverse order
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prompts_id_asc"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prompt_translations_lang"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prompt_translations_prompt_lang"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prompts_title_not_null"`);
  }
}
